import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { getSupabaseAdminClient } from '@/lib/supabase';
import { SubscriptionTier, getSubscriptionLimits } from '@/config/subscription-limits';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get request body
    const { name, category, encryptedFile, encryptionKey, fileType, fileSize } = await request.json();

    if (!name || !category || !encryptedFile || !encryptionKey) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate the encryption key
    if (typeof encryptionKey !== 'string' || encryptionKey.length < 10) {
      console.error('Invalid encryption key format:', { keyType: typeof encryptionKey, keyLength: encryptionKey?.length });
      return NextResponse.json(
        { error: 'Invalid encryption key format' },
        { status: 400 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Check user's subscription tier and storage limits
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('subscription_tier')
      .eq('id', user.id)
      .single();

    // If there's an error or no profile, assume free tier
    const tier = (profile?.subscription_tier as SubscriptionTier) || 'free';

    // Get the limits for the user's tier
    const limits = getSubscriptionLimits(tier);
    const maxStorageBytes = limits.maxVaultStorageGB * 1024 * 1024 * 1024;

    // Calculate current storage usage
    const { data: documents } = await supabaseAdmin
      .from('vault_documents')
      .select('file_size')
      .eq('user_id', user.id);

    let currentStorageUsed = 0;
    if (documents && documents.length > 0) {
      currentStorageUsed = documents.reduce((total: number, doc: { file_size: number }) => total + (doc.file_size || 0), 0);
    }

    // Check if adding this file would exceed the storage limit
    const fileBytes = fileSize || (encryptedFile.data ? atob(encryptedFile.data).length : 0);
    if (currentStorageUsed + fileBytes > maxStorageBytes) {
      return NextResponse.json(
        {
          error: 'Storage limit exceeded',
          currentUsage: Math.round(currentStorageUsed / (1024 * 1024)), // in MB
          maxStorage: limits.maxVaultStorageGB * 1024, // in MB
          upgrade: true
        },
        { status: 403 }
      );
    }

    // Ensure the documents bucket exists
    try {
      const { data: buckets } = await supabaseAdmin.storage.listBuckets();
      const bucketExists = buckets?.some((b: { name: string }) => b.name === 'documents');

      if (!bucketExists) {
        await supabaseAdmin.storage.createBucket('documents', {
          public: true,
          fileSizeLimit: 5368709120, // 5GB
        });
      }
    } catch (error) {
      console.error('Error checking/creating bucket:', error);
      // Continue anyway, as the bucket might already exist
    }

    // Generate file path
    const timestamp = Date.now();
    const fileName = encryptedFile.name || `document_${timestamp}`;
    const filePath = `${user.id}/${timestamp}_${fileName}`;

    // Convert base64 to Uint8Array
    const binaryString = atob(encryptedFile.data);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // Upload the file
    const { error: uploadError } = await supabaseAdmin.storage
      .from('documents')
      .upload(filePath, bytes, {
        contentType: fileType || 'application/octet-stream',
        upsert: true
      });

    if (uploadError) {
      console.error('Storage upload error:', uploadError);
      return NextResponse.json(
        { error: `Failed to upload file: ${uploadError.message}` },
        { status: 500 }
      );
    }

    // Store document metadata in the database
    const documentMetadata = {
      name,
      user_id: user.id,
      category,
      file_path: filePath,
      file_name: fileName,
      file_type: fileType || 'application/octet-stream',
      file_size: fileSize || bytes.length,
      is_encrypted: true,
      encryption_key: encryptionKey
    };

    console.log('Inserting document into vault_documents:', {
      ...documentMetadata,
      encryption_key: '[REDACTED]' // Don't log the actual key
    });

    const { data: documentData, error: dbError } = await supabaseAdmin
      .from('vault_documents')
      .insert(documentMetadata)
      .select()
      .single();

    console.log('Insert result:', documentData);
    console.log('Insert error:', dbError);

    if (dbError) {
      console.error('Database insert error:', dbError);

      // Try to clean up the uploaded file
      await supabaseAdmin.storage
        .from('documents')
        .remove([filePath]);

      return NextResponse.json(
        { error: `Failed to store document metadata: ${dbError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(documentData);
  } catch (error: any) {
    console.error('Error in document upload API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
