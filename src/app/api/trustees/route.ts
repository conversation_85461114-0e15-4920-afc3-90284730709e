import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { supabaseAdmin } from '@/lib/supabase-server';
import { checkResourceLimit } from '@/middleware/subscription-check';
import { ensureAuthUserMapping } from '@/lib/auth-mapping-utils';

export async function GET(_request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    const authUserId = await ensureAuthUserMapping(user.id);

    if (!authUserId) {
      return NextResponse.json(
        { error: 'Failed to get or create auth user mapping' },
        { status: 500 }
      );
    }

    // Get trustees for the user
    const { data, error } = await supabaseAdmin
      .from('trustees')
      .select('*')
      .eq('user_id', authUserId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching trustees:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trustees' },
        { status: 500 }
      );
    }

    // If no trustees found, return an empty array (not an error)
    if (!data) {
      return NextResponse.json([]);
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in trustees API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.first_name || !body.last_name || !body.email || !body.permissions) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    // This is because the trustees table has a foreign key constraint to auth.users
    const authUserId = await ensureAuthUserMapping(user.id);

    if (!authUserId) {
      return NextResponse.json(
        { error: 'Failed to get or create auth user mapping' },
        { status: 500 }
      );
    }

    // Check if the user has reached their trustee limit
    const { hasReachedLimit, currentCount, maxAllowed } = await checkResourceLimit(
      user.id,
      'trustees',
      'trustees'
    );

    if (hasReachedLimit) {
      return NextResponse.json(
        {
          error: 'Trustee limit reached',
          limit: maxAllowed,
          current: currentCount,
          upgrade: true
        },
        { status: 403 }
      );
    }

    // Check if the trustee email already exists in our system
    // First check in profiles table
    const { data: existingProfile } = await supabaseAdmin
      .from('profiles')
      .select('id, email')
      .eq('email', body.email.toLowerCase())
      .maybeSingle();

    // Also check in custom_users table to be thorough
    const { data: existingCustomUser } = await supabaseAdmin
      .from('custom_users')
      .select('id, email')
      .eq('email', body.email.toLowerCase())
      .maybeSingle();

    const existingUser = existingProfile || existingCustomUser;

    console.log(`Creating trustee with email ${body.email}. Existing user:`, existingUser ? `Found with ID ${existingUser.id}` : 'Not found');

    // Create the trustee data
    const trusteeData = {
      user_id: authUserId, // Use the auth.users ID
      trustee_email: body.email,
      trustee_user_id: existingUser?.id || null,
      first_name: body.first_name,
      last_name: body.last_name,
      relationship: body.relationship || '',
      status: 'pending',
      permissions: body.permissions,
      invitation_sent_at: null // Set to null since we're not sending the invitation yet
    };

    try {
      // Now try to insert the trustee
      const { data, error: insertError } = await supabaseAdmin
        .from('trustees')
        .insert(trusteeData)
        .select();

      if (insertError) {
        console.error('Error creating trustee:', insertError);
        return NextResponse.json(
          { error: `Failed to create trustee: ${insertError.message}` },
          { status: 500 }
        );
      }

      if (!data || data.length === 0) {
        return NextResponse.json(
          { error: 'No data returned after trustee creation' },
          { status: 500 }
        );
      }

      return NextResponse.json(data[0]);
    } catch (insertError: any) {
      console.error('Exception creating trustee:', insertError);
      return NextResponse.json(
        { error: `Exception creating trustee: ${insertError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error in trustees API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const { id, ...trusteeData } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Trustee ID is required' },
        { status: 400 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    const authUserId = await ensureAuthUserMapping(user.id);

    if (!authUserId) {
      return NextResponse.json(
        { error: 'Failed to get or create auth user mapping' },
        { status: 500 }
      );
    }

    // First, verify that the trustee belongs to the user
    const { error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('id')
      .eq('id', id)
      .eq('user_id', authUserId)
      .single();

    if (fetchError) {
      console.error('Error fetching trustee:', fetchError);
      return NextResponse.json(
        { error: 'Trustee not found or you do not have permission to update it' },
        { status: 404 }
      );
    }

    // Update the trustee
    const { data, error: updateError } = await supabaseAdmin
      .from('trustees')
      .update({
        ...trusteeData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', authUserId)
      .select();

    if (updateError) {
      console.error('Error updating trustee:', updateError);
      return NextResponse.json(
        { error: `Failed to update trustee: ${updateError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error updating trustee:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update trustee' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the trustee ID from the URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Trustee ID is required' },
        { status: 400 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    const authUserId = await ensureAuthUserMapping(user.id);

    if (!authUserId) {
      return NextResponse.json(
        { error: 'Failed to get or create auth user mapping' },
        { status: 500 }
      );
    }

    // First, verify that the trustee belongs to the user
    const { error: fetchError } = await supabaseAdmin
      .from('trustees')
      .select('id')
      .eq('id', id)
      .eq('user_id', authUserId)
      .single();

    if (fetchError) {
      console.error('Error fetching trustee:', fetchError);
      return NextResponse.json(
        { error: 'Trustee not found or you do not have permission to delete it' },
        { status: 404 }
      );
    }

    // Delete the trustee
    const { error: deleteError } = await supabaseAdmin
      .from('trustees')
      .delete()
      .eq('id', id)
      .eq('user_id', authUserId);

    if (deleteError) {
      console.error('Error deleting trustee:', deleteError);
      return NextResponse.json(
        { error: `Failed to delete trustee: ${deleteError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error deleting trustee:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete trustee' },
      { status: 500 }
    );
  }
}
