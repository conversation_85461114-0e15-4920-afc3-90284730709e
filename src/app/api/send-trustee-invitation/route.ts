import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { getSupabaseAdminClient } from '@/lib/supabase';
import { ensureAuthUserMapping } from '@/lib/auth-mapping-utils';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabase = getSupabaseAdminClient();

    const {
      inviterName,
      inviterEmail,
      trusteeName,
      trusteeEmail,
      permissions,
      message,
      inviteId,
    } = await request.json();

    // Validate required fields
    if (!trusteeEmail || !inviteId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate a unique invitation link
    const invitationLink = `${process.env.NEXT_PUBLIC_APP_URL}/trustee/accept?id=${inviteId}`;

    // Call the Supabase function to send the email
    try {
      console.log('Sending invitation email to:', trusteeEmail, 'from:', inviterEmail);

      // Make sure we have valid data before sending
      if (!trusteeEmail) {
        return NextResponse.json(
          { error: 'Trustee email is required' },
          { status: 400 }
        );
      }

      // Use default values if data is missing
      const safeInviterName = inviterName || 'A Legalock User';
      const safeInviterEmail = inviterEmail || '<EMAIL>';
      const safeTrusteeName = trusteeName || 'Trustee';

      const functionResponse = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/send-trustee-invitation`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          },
          body: JSON.stringify({
            inviterName: safeInviterName,
            inviterEmail: safeInviterEmail,
            trusteeName: safeTrusteeName,
            trusteeEmail,
            permissions,
            inviteId,
          }),
        }
      );

      if (!functionResponse.ok) {
        const errorData = await functionResponse.json();
        console.error('Error from Supabase function:', errorData);
        return NextResponse.json(
          { error: 'Failed to send invitation email' },
          { status: 500 }
        );
      }

      console.log('Email sent successfully via Supabase function');
    } catch (emailError) {
      console.error('Error calling Supabase function:', emailError);
      return NextResponse.json(
        { error: 'Failed to send invitation email' },
        { status: 500 }
      );
    }

    // Use the ensureAuthUserMapping utility to get or create the auth user mapping
    const authUserId = await ensureAuthUserMapping(user.id);

    if (!authUserId) {
      console.error('Failed to get or create auth user mapping');
      return NextResponse.json(
        { error: 'Failed to get or create auth user mapping' },
        { status: 500 }
      );
    }

    // Update the trustee record to mark the invitation as sent
    const { error: updateError } = await supabase
      .from('trustees')
      .update({
        invitation_sent_at: new Date().toISOString(),
      })
      .eq('id', inviteId);

    if (updateError) {
      throw updateError;
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error sending trustee invitation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to send invitation' },
      { status: 500 }
    );
  }
}
