import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Use the user's ID directly
    const userId = user.id;

    // Log for debugging
    console.log('Using user ID:', userId);

    // Get time capsules for the user
    const { data, error } = await supabaseAdmin
      .from('time_capsules')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching time capsules:', error);
      return NextResponse.json(
        { error: 'Failed to fetch time capsules' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in GET /api/time-capsules:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.recipient_email || !body.delivery_date) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate recipient first and last name
    if (!body.recipient_first_name || !body.recipient_last_name) {
      return NextResponse.json(
        { error: 'Recipient first name and last name are required' },
        { status: 400 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // First, try to get the auth.users record for this user
    const { data: authUser, error: authUserError } = await supabaseAdmin
      .from('auth_users_mapping')
      .select('auth_user_id')
      .eq('custom_user_id', user.id)
      .single();

    // If mapping doesn't exist, create it
    if (authUserError || !authUser) {
      console.log('Auth user mapping not found, attempting to create it...');

      // Get the auth user with the same email
      const { data: authUserByEmail, error: authUserByEmailError } = await supabaseAdmin
        .from('auth.users')
        .select('id')
        .eq('email', user.email)
        .single();

      if (authUserByEmailError || !authUserByEmail) {
        console.error('Error finding auth user by email:', authUserByEmailError);

        // Continue with user.id as fallback
        console.log('Using custom user ID as fallback');
      } else {
        // Create the mapping
        const { error: createMappingError } = await supabaseAdmin
          .from('auth_users_mapping')
          .insert({
            custom_user_id: user.id,
            auth_user_id: authUserByEmail.id
          });

        if (createMappingError) {
          console.error('Error creating auth user mapping:', createMappingError);
        } else {
          console.log('Auth user mapping created successfully');
        }
      }
    }

    // Generate a random access code for recipient authentication
    const generateAccessCode = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let code = '';
      for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return code;
    };

    // Create the time capsule
    const capsuleData: any = {
      user_id: user.id,
      title: body.title,
      message: body.message || '',
      recipient_email: body.recipient_email,
      delivery_date: body.delivery_date,
      delivery_hour: body.delivery_hour || 12,
      status: 'scheduled',
      access_code: generateAccessCode(),
      sender_name: user.email,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add recipient name fields if provided
    if (body.recipient_first_name) {
      capsuleData.recipient_first_name = body.recipient_first_name;
    }

    if (body.recipient_last_name) {
      capsuleData.recipient_last_name = body.recipient_last_name;
    }

    // Add combined recipient_name for backward compatibility if both first and last name are provided
    if (body.recipient_first_name && body.recipient_last_name) {
      capsuleData.recipient_name = `${body.recipient_first_name} ${body.recipient_last_name}`;
    } else if (body.recipient_name) {
      capsuleData.recipient_name = body.recipient_name;
    }

    const { data, error } = await supabaseAdmin
      .from('time_capsules')
      .insert(capsuleData)
      .select()
      .single();

    if (error) {
      console.error('Error creating time capsule:', error);
      return NextResponse.json(
        { error: 'Failed to create time capsule' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in POST /api/time-capsules:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the ID from the query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Time capsule ID is required' },
        { status: 400 }
      );
    }

    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Use the user's ID directly
    const userId = user.id;

    // Log for debugging
    console.log('Using user ID for delete:', userId);

    // First, verify that the time capsule belongs to the user
    const { data: existingCapsule, error: fetchError } = await supabaseAdmin
      .from('time_capsules')
      .select('id')
      .eq('id', id)
      .eq('user_id', userId)
      .single();

    if (fetchError) {
      console.error('Error fetching time capsule:', fetchError);
      return NextResponse.json(
        { error: 'Time capsule not found or you do not have permission to delete it' },
        { status: 404 }
      );
    }

    // Delete the time capsule
    const { error: deleteError } = await supabaseAdmin
      .from('time_capsules')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Error deleting time capsule:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete time capsule' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in DELETE /api/time-capsules:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
