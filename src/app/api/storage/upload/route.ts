import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getSessionByToken, getUserById } from '@/lib/auth-utils';
import { getSupabaseAdminClient } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const cookieStore = cookies();
    const sessionToken = cookieStore.get('session_token')?.value;

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the session
    const session = await getSessionByToken(sessionToken);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user
    const user = await getUserById(session.user_id);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const bucket = formData.get('bucket') as string;
    const path = formData.get('path') as string;

    if (!file || !bucket || !path) {
      return NextResponse.json(
        { error: 'Missing required fields: file, bucket, or path' },
        { status: 400 }
      );
    }

    // Get the admin client
    const supabaseAdmin = getSupabaseAdminClient();

    // Try to create the storage bucket if it doesn't exist
    try {
      // Check if the bucket exists
      const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();

      if (listError) {
        console.error('Error listing buckets:', listError);
      } else {
        const bucketExists = buckets.some((b: { name: string }) => b.name === bucket);

        if (!bucketExists) {
          // Create the bucket
          const { error: createError } = await supabaseAdmin.storage.createBucket(bucket, {
            public: true,
            fileSizeLimit: 5368709120, // 5GB
            allowedMimeTypes: ['*/*', 'application/octet-stream', 'application/encrypted', 'application/pdf', 'image/*', 'video/*', 'audio/*', 'text/*'],
          });

          if (createError) {
            console.error(`Error creating bucket ${bucket}:`, createError);
          } else {
            console.log(`Bucket ${bucket} created successfully.`);

            // Create policies for the bucket
            await supabaseAdmin.storage.from(bucket).createPolicy('authenticated can select', {
              name: 'authenticated can select',
              definition: `(bucket_id = '${bucket}' AND auth.uid()::text = (storage.foldername(name))[1])`,
              operation: 'SELECT',
              role: 'authenticated'
            });

            await supabaseAdmin.storage.from(bucket).createPolicy('authenticated can insert', {
              name: 'authenticated can insert',
              definition: `(bucket_id = '${bucket}' AND auth.uid()::text = (storage.foldername(name))[1])`,
              operation: 'INSERT',
              role: 'authenticated'
            });

            await supabaseAdmin.storage.from(bucket).createPolicy('authenticated can update', {
              name: 'authenticated can update',
              definition: `(bucket_id = '${bucket}' AND auth.uid()::text = (storage.foldername(name))[1])`,
              operation: 'UPDATE',
              role: 'authenticated'
            });

            await supabaseAdmin.storage.from(bucket).createPolicy('authenticated can delete', {
              name: 'authenticated can delete',
              definition: `(bucket_id = '${bucket}' AND auth.uid()::text = (storage.foldername(name))[1])`,
              operation: 'DELETE',
              role: 'authenticated'
            });
          }
        }
      }
    } catch (error) {
      console.log('Error creating storage bucket:', error);
      // Continue anyway, as the bucket might already exist
    }

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = new Uint8Array(arrayBuffer);

    // Upload the file
    const { data, error } = await supabaseAdmin.storage
      .from(bucket)
      .upload(`${user.id}/${path}`, fileBuffer, {
        contentType: file.type || 'application/octet-stream', // Use octet-stream as fallback
        upsert: true
      });

    if (error) {
      console.error('Error uploading file:', error);
      return NextResponse.json(
        { error: `Failed to upload file: ${error.message}` },
        { status: 500 }
      );
    }

    // Get the public URL
    const { data: { publicUrl } } = supabaseAdmin.storage
      .from(bucket)
      .getPublicUrl(`${user.id}/${path}`);

    return NextResponse.json({
      path: data.path,
      fullPath: publicUrl
    });
  } catch (error: any) {
    console.error('Error in storage upload API:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
