
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './auth-context';
import { toast } from 'sonner';

type SubscriptionPlan = 'free' | 'premium';

interface SubscriptionContextType {
  plan: SubscriptionPlan;
  isLoading: boolean;
  isSubscribed: boolean;
  subscriptionDetails: any;
  checkSubscription: () => Promise<void>;
  createCheckout: (plan: 'premium') => Promise<string | null>;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const auth = useAuth();
  const { user } = auth || {};
  const [plan, setPlan] = useState<SubscriptionPlan>('free');
  const [isLoading, setIsLoading] = useState(!auth);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState(null);

  const checkSubscription = async () => {
    if (!user) {
      setIsLoading(false);
      setPlan('free');
      setIsSubscribed(false);
      setSubscriptionDetails(null);
      return;
    }

    try {
      setIsLoading(true);

      // Use the Next.js API route instead of Supabase Edge Function
      const response = await fetch('/api/stripe/subscription');

      if (!response.ok) {
        throw new Error('Failed to fetch subscription status');
      }

      const data = await response.json();

      setPlan(data.plan);
      setIsSubscribed(data.isSubscribed);
      setSubscriptionDetails(data.subscription);
    } catch (error: any) {
      console.error('Error checking subscription status:', error);
      toast.error('Failed to check subscription status');
      // Set default values on error
      setPlan('free');
      setIsSubscribed(false);
      setSubscriptionDetails(null);
    } finally {
      setIsLoading(false);
    }
  };

  const createCheckout = async (selectedPlan: 'premium'): Promise<string | null> => {
    if (!user) {
      toast.error('You must be logged in to subscribe');
      return null;
    }

    try {
      // Use the Next.js API route instead of Supabase Edge Function
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan: selectedPlan }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const data = await response.json();
      return data.url;
    } catch (error: any) {
      console.error('Error creating checkout session:', error);
      toast.error(error.message || 'Failed to create checkout session');
      return null;
    }
  };

  useEffect(() => {
    if (user) {
      checkSubscription();
    }
  }, [user]);

  return (
    <SubscriptionContext.Provider
      value={{
        plan,
        isLoading,
        isSubscribed,
        subscriptionDetails,
        checkSubscription,
        createCheckout,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};
